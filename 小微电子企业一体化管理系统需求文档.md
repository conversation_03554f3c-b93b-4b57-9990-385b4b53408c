# 小微电子企业一体化管理系统需求文档

## 文档信息
- **文档版本**: V1.0
- **编写日期**: 2025-08-18
- **文档状态**: 初稿
- **编写人**: [待填写]
- **审核人**: [待填写]
- **批准人**: [待填写]

---

## 1. 引言

### 1.1 编写目的
本文档旨在详细说明"小微电子企业一体化管理系统"的功能性与非功能性需求。其目的是为项目参与各方（包括决策层、开发团队、业务用户）提供一个共同的理解基础，作为后续系统设计、开发、测试和验收的核心依据。

### 1.2 项目背景
我司是一家典型的小微电子企业，当前正面临高速发展与管理滞后之间的尖锐矛盾。业务模式以"小批量、多批次、高定制、短交期"为特点，在研发、供应链、生产、质量等环节存在显著的管理痛点。

**现状问题**：
- 大量工作依赖Excel、邮件和口头沟通
- 信息孤岛严重，BOM变更混乱
- 库存积压与缺料并存
- 生产计划频繁中断
- 质量问题难以追溯
- 严重制约公司盈利能力和扩张潜力

### 1.3 项目目标
本项目的核心目标是构建一个以BOM为数据核心，贯穿研发、销售、采购、库存、生产、质量、成本全流程的一体化管理平台，实现：

1. **数据同源，消除孤岛**：建立全公司统一的物料、BOM、供应商、客户数据库，实现"一处维护，处处使用"
2. **流程在线，权责清晰**：将核心业务流程（如工程变更、采购申请）固化到系统中，实现过程透明、可追溯
3. **库存精准，降本增效**：实现库存数据的实时、准确，目标降低呆滞库存30%，减少因缺料导致的生产停线50%
4. **质量可溯，提升信誉**：建立从供应商来料到成品出货的全程质量追溯链条，实现客户投诉问题2小时内定位到具体批次
5. **决策有据，管理升级**：为管理层提供实时的业务数据看板，支撑科学决策
6. **业务财务一体，成本透明**：实现订单维度的成本实时归集（物料、人工、制造费用），清晰掌握每个订单的毛利情况
7. **订单全程可视，提升交付**：建立以销售订单为驱动的全流程跟踪视图，一键掌握订单实时状态

### 1.4 项目范围

**包含范围**：
- 研发设计管理 (PLM核心功能)
- 销售与订单全程跟踪
- 采购与供应链管理
- 库存与仓储管理
- 生产计划与执行管理 (MES核心功能)
- 质量管理
- 成本与财务集成

**不包含范围**：
- 专业的财务总账
- 人力资源管理（HR）
- 客户关系管理（CRM）的深度功能

**注**：需预留标准接口以便未来集成。

---

## 2. 总体设计

### 2.1 用户角色与职责
系统采用"基于角色的权限分配"模式，而非固化的部门岗位。管理员可以自由地为一个用户账号分配一个或多个角色，以完美匹配"一人多岗"的现状。

| 核心业务角色 | 核心职责 | 关注的系统功能 | 典型"一人多岗"组合 |
|-------------|----------|----------------|-------------------|
| 产品研发角色 | 创建和维护物料、BOM，发起工程变更 | 物料库、BOM管理、ECN流程 | 老板/技术总监 = 产品研发 + 项目管理 + 销售支持 |
| 采购执行角色 | 供应商管理，执行采购，跟踪订单 | 供应商库、采购订单、来料跟踪 | 采购员 = 采购执行 + 仓库管理 |
| 仓库管理角色 | 物料出入库、盘点，保证账实相符 | 出入库单、库存查询、条码管理 | |
| 生产计划角色 | 制定生产计划，下达生产工单 | 生产订单、工单排产、物料齐套检查 | 生产主管 = 生产计划 + 车间执行 + 质量监督 |
| 车间执行角色 | 生产任务报工，物料领用 | 生产报工、WIP监控、工时统计 | |
| 质量监督角色 | 执行质量检验，处理质量问题 | 检验标准、检验记录、质量追溯 | |
| 销售支持角色 | 创建和管理销售订单，跟进交付 | 销售订单录入、订单评审、订单状态跟踪 | 销售助理 = 销售支持 + 仓库管理（发货） |
| 财务监控角色 | 监控成本、应收应付，核算订单利润 | 成本归集、订单毛利分析、应收应付账款 | 老板/财务 = 财务监控 + 总经理看板 |
| 系统管理员角色 | 维护系统基础数据，分配用户角色与权限 | 用户管理、角色权限配置、系统设置 | 老板/行政 |

### 2.2 系统架构
建议采用云原生（SaaS）架构，通过浏览器即可访问。系统应采用模块化设计，确保各模块之间数据互通、耦合度低，便于分阶段实施与未来扩展。

---

## 3. 功能性需求 (FR)

### FR-1. 研发与设计管理

#### FR-1.1 物料主数据管理
- **【必须】** 建立统一的物料编码体系，所有物料有唯一的编码
- **【必须】** 维护物料的详细属性：规格、型号、品牌、封装、供应商、生命周期状态（量产、停产等）
- **【期望】** 支持替代料管理

#### FR-1.2 BOM管理
- **【必须】** 支持多级BOM的创建、编辑、查询
- **【必须】** 严格的版本控制，每次修改生成新版本，旧版本可追溯，并记录变更历史
- **【必须】** BOM状态管理（设计中、审核中、已发布），只有"已发布"的BOM才能被生产和采购使用

#### FR-1.3 工程变更管理 (ECN/ECO)
- **【必须】** 提供标准化的ECN电子审批流程（申请-审核-批准-执行）
- **【必须】** ECN流程能自动通知到采购、生产、品质等相关人员
- **【必须】** 记录ECN的变更原因、变更内容、影响范围

### FR-2. 销售与订单全程跟踪

#### FR-2.1 销售订单管理
- **【必须】** 创建销售订单时，可进行简单的订单评审流程（如技术可行性、交期、成本评估）
- **【必须】** 系统自动为每个销售订单生成唯一跟踪号

#### FR-2.2 订单驱动业务
- **【必须】** 生产订单、采购申请能直接关联到具体的销售订单号
- **【期望】** 支持按单设计（MTO）、按单生产（MTO）模式，BOM和工艺路线可针对特定订单进行调整

#### FR-2.3 订单全景视图
- **【核心亮点】** 提供"订单跟踪驾驶舱"。输入销售订单号，即可在一个页面内查看到订单的设计、采购、生产、库存、发货、成本等各环节实时状态

### FR-3. 供应链与采购管理

#### FR-3.1 供应商管理
- 建立合格供应商档案，记录其资质、交付能力、质量评级

#### FR-3.2 采购管理
- 系统能根据销售订单或安全库存自动生成采购需求
- 支持采购申请、采购订单的创建和审批流程
- 实时跟踪订单状态

### FR-4. 库存与仓储管理

#### FR-4.1 实时库存
- 所有出入库操作必须通过系统完成，确保库存数据的实时性和准确性

#### FR-4.2 出入库管理
- 支持采购入库、生产领料、成品入库、销售出库等多种业务类型

#### FR-4.3 库存预警
- 支持设置物料的安全库存、最高库存，并提供预警提醒和呆滞料分析报表

#### FR-4.4 条码支持
- **【期望】** 支持通过扫描条码/二维码进行快速出入库，减少人为错误

### FR-5. 生产计划与执行管理

#### FR-5.1 生产订单管理
- 系统能根据销售订单自动创建生产订单，并自动关联正确的BOM版本

#### FR-5.2 生产排产
- 提供可视化的排产看板，支持手动调整
- 排产前能进行物料齐套检查，预警缺料风险

#### FR-5.3 车间执行
- 支持生产任务的下达和生产报工，实时反馈生产进度和数量

### FR-6. 质量管理

#### FR-6.1 质量检验
- 支持来料检（IQC）、过程检（IPQC）、成品检（FQC）的记录及不合格品处理流程

#### FR-6.2 质量追溯
- 建立完整的产品追溯档案
- 通过成品序列号，能反向追溯到其所用的关键元器件批次、生产工单、操作人员、检验记录

### FR-7. 成本与财务集成

#### FR-7.1 成本要素管理
- 维护物料的标准采购成本，并可设定工序的标准工时和费率

#### FR-7.2 订单成本自动归集
- 生产领料和报工时，系统自动将实际材料成本、人工及制造费用归集到对应的销售订单

#### FR-7.3 订单毛利分析
- 提供订单毛利分析报表，实时计算每个订单的毛利和毛利率

#### FR-7.4 应收应付管理 (简化版)
- 自动生成应收应付提醒，并提供简单的账龄分析

---

## 4. 非功能性需求 (NFR)

| 需求类别 | 具体要求 |
|----------|----------|
| **NFR-1. 性能** | 系统核心页面加载时间应小于3秒，复杂报表查询时间应小于10秒 |
| **NFR-2. 易用性** | 界面设计直观、简洁。新员工经过不超过4小时的培训即可上手核心操作。支持用户自定义常用功能快捷入口或工作台 |
| **NFR-3. 安全性** | 采用基于角色的权限控制，不同岗位的用户只能访问和操作其权限范围内的数据。关键操作有日志记录 |
| **NFR-4. 可扩展性** | 系统应采用模块化设计，方便未来增加新功能或与财务软件、CRM等第三方系统集成 |
| **NFR-5. 部署方式** | 优先考虑SaaS（云部署）模式，以降低我司的初期投入和IT运维成本 |
| **NFR-6. 成本效益** | 供应商应提供清晰的定价方案（如按需订阅、按用户数付费），避免一次性大额投入和隐藏费用 |
| **NFR-7. 灵活性** | 核心业务流程（如ECN、采购审批）应支持可配置的审批节点和审批流，以适应公司未来组织架构的变化 |

---

## 5. 实施与验收

### 5.1 实施规划（建议分阶段上线）

#### 第一阶段（3个月）：核心生存包
- **上线模块**：销售订单、研发管理(物料/BOM)、采购管理、库存管理、订单成本归集
- **目标**：打通从订单到采购的核心链条，管住BOM和库存，并能看到每个订单大致的材料成本

#### 第二阶段（3个月）：生产与交付闭环
- **上线模块**：生产管理、质量管理、订单全景视图
- **目标**：实现生产过程透明化和质量可追溯，让管理者和销售能实时跟踪订单

#### 第三阶段（持续优化）：数据分析与集成
- **上线模块**：数据看板、报表中心，并考虑与财务软件集成
- **目标**：利用数据驱动业务改进

### 5.2 验收标准

1. 所有在本文档"功能性需求"中标记为【必须】的功能点均已实现且运行稳定
2. 系统能成功跑通一个从"销售订单 → 生产计划 → 采购 → 生产 → 入库 → 发货"的完整业务闭环
3. 系统数据准确性（如库存准确率）达到98%以上
4. 订单全程跟踪视图能准确、实时地反映各环节状态
5. 订单毛利分析报表的数据与手动核算结果基本一致
6. 完成对所有用户角色的操作培训，并提供完整的用户手册和技术支持文档

---

## 附录：名词解释

- **BOM (Bill of Materials)**: 物料清单
- **ECN (Engineering Change Notice)**: 工程变更通知
- **PLM (Product Lifecycle Management)**: 产品生命周期管理
- **ERP (Enterprise Resource Planning)**: 企业资源计划
- **MES (Manufacturing Execution System)**: 制造执行系统
- **IQC/IPQC/FQC**: 来料/过程/最终质量控制
- **MTO (Make to Order)**: 按单生产
- **WIP (Work in Process)**: 在制品

---

**文档结束**
